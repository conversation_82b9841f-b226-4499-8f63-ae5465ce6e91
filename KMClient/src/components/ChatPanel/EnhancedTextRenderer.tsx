/**
 * 增強文本渲染組件
 * 支持 Markdown 渲染和 URL 連結處理
 */

import React from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeRaw from 'rehype-raw';
import rehypeSanitize from 'rehype-sanitize';
import { Image } from 'antd';

interface EnhancedTextRendererProps {
  content: string;
  isUser?: boolean;
}

const EnhancedTextRenderer: React.FC<EnhancedTextRendererProps> = ({
  content,
  isUser = false
}) => {
  // 檢查內容是否包含 Markdown 語法
  const hasMarkdownSyntax = (text: string): boolean => {
    const markdownPatterns = [
      /\*\*.*?\*\*/,        // 粗體 **text**
      /\*.*?\*/,            // 斜體 *text*
      /^#{1,6}\s/m,         // 標題 # ## ###
      /^\s*[-*+]\s/m,       // 無序列表 - * +
      /^\s*\d+\.\s/m,       // 有序列表 1. 2.
      /\[.*?\]\(.*?\)/,     // 連結 [text](url)
      /```[\s\S]*?```/,     // 代碼塊 ```code```
      /`.*?`/,              // 行內代碼 `code`
      /^\s*>\s/m,           // 引用 > text
      /^\s*\|.*\|/m,        // 表格 | col1 | col2 |
    ];
    
    return markdownPatterns.some(pattern => pattern.test(text));
  };

  // 檢查 URL 是否為圖片
  const isImageUrl = (url: string): boolean => {
    const imageExtensions = /\.(jpg|jpeg|png|gif|webp|bmp|svg)(\?.*)?$/i;
    return imageExtensions.test(url);
  };

  // 處理文本中的換行符，將 \n 轉換為 <br> 標籤
  const renderTextWithLineBreaks = (text: string) => {
    return text.split('\n').map((line, index, array) => (
      <React.Fragment key={index}>
        {line}
        {index < array.length - 1 && <br />}
      </React.Fragment>
    ));
  };

  // 處理純文本中的 URL
  const renderTextWithLinks = (text: string) => {
    // URL 正則表達式
    const urlRegex = /(https?:\/\/[^\s]+|www\.[^\s]+)/g;
    const parts = text.split(urlRegex);
    
    return parts.map((part, index) => {
      if (urlRegex.test(part)) {
        // 確保 URL 有協議
        const url = part.startsWith('www.') ? `https://${part}` : part;
        
        // 如果是圖片 URL，顯示為縮略圖
        if (isImageUrl(url)) {
          return (
            <div key={index} className="inline-block my-2">
              <Image
                src={url}
                alt="圖片"
                style={{ 
                  maxWidth: '200px', 
                  maxHeight: '200px',
                  borderRadius: '8px'
                }}
                preview={{
                  src: url,
                }}
                fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
              />
            </div>
          );
        } else {
          // 普通連結
          return (
            <a
              key={index}
              href={url}
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-500 hover:text-blue-700 underline"
              style={{
                color: isUser ? 'rgba(255,255,255,0.9)' : 'var(--color-neon-blue)',
                textDecoration: 'underline'
              }}
            >
              {part}
            </a>
          );
        }
      }
      // 對於非URL文本，處理換行符
      return (
        <span key={index}>
          {renderTextWithLineBreaks(part)}
        </span>
      );
    });
  };

  // 自定義 Markdown 組件
  const markdownComponents = {
    // 連結組件
    a: ({ href, children, ...props }: any) => {
      if (isImageUrl(href)) {
        return (
          <div className="inline-block my-2">
            <Image
              src={href}
              alt={children}
              style={{ 
                maxWidth: '200px', 
                maxHeight: '200px',
                borderRadius: '8px'
              }}
              preview={{
                src: href,
              }}
            />
          </div>
        );
      }
      
      return (
        <a
          href={href}
          target="_blank"
          rel="noopener noreferrer"
          style={{
            color: isUser ? 'rgba(255,255,255,0.9)' : 'var(--color-neon-blue)',
            textDecoration: 'underline'
          }}
          {...props}
        >
          {children}
        </a>
      );
    },
    
    // 圖片組件
    img: ({ src, alt, ...props }: any) => (
      <div className="my-2">
        <Image
          src={src}
          alt={alt}
          style={{ 
            maxWidth: '200px', 
            maxHeight: '200px',
            borderRadius: '8px'
          }}
          preview={{
            src: src,
          }}
          {...props}
        />
      </div>
    ),
    
    // 標題組件
    h1: ({ children, ...props }: any) => (
      <h1 
        style={{ 
          color: isUser ? 'white' : 'var(--color-text-primary)',
          fontSize: '1.5em',
          fontWeight: 'bold',
          marginBottom: '0.5em'
        }} 
        {...props}
      >
        {children}
      </h1>
    ),
    
    h2: ({ children, ...props }: any) => (
      <h2 
        style={{ 
          color: isUser ? 'white' : 'var(--color-text-primary)',
          fontSize: '1.3em',
          fontWeight: 'bold',
          marginBottom: '0.5em'
        }} 
        {...props}
      >
        {children}
      </h2>
    ),
    
    h3: ({ children, ...props }: any) => (
      <h3 
        style={{ 
          color: isUser ? 'white' : 'var(--color-text-primary)',
          fontSize: '1.1em',
          fontWeight: 'bold',
          marginBottom: '0.5em'
        }} 
        {...props}
      >
        {children}
      </h3>
    ),
    
    // 段落組件
    p: ({ children, ...props }: any) => (
      <p 
        style={{ 
          color: isUser ? 'white' : 'var(--color-text-primary)',
          marginBottom: '0.5em',
          lineHeight: '1.5'
        }} 
        {...props}
      >
        {children}
      </p>
    ),
    
    // 列表組件
    ul: ({ children, ...props }: any) => (
      <ul 
        style={{ 
          color: isUser ? 'white' : 'var(--color-text-primary)',
          marginLeft: '1.5em',
          marginBottom: '0.5em'
        }} 
        {...props}
      >
        {children}
      </ul>
    ),
    
    ol: ({ children, ...props }: any) => (
      <ol 
        style={{ 
          color: isUser ? 'white' : 'var(--color-text-primary)',
          marginLeft: '1.5em',
          marginBottom: '0.5em'
        }} 
        {...props}
      >
        {children}
      </ol>
    ),
    
    // 代碼組件
    code: ({ children, className, ...props }: any) => {
      const isInline = !className;
      
      if (isInline) {
        return (
          <code
            style={{
              backgroundColor: isUser ? 'rgba(255,255,255,0.2)' : 'var(--color-surface-secondary)',
              color: isUser ? 'white' : 'var(--color-text-primary)',
              padding: '2px 4px',
              borderRadius: '4px',
              fontSize: '0.9em',
              fontFamily: 'monospace'
            }}
            {...props}
          >
            {children}
          </code>
        );
      }
      
      return (
        <pre
          style={{
            backgroundColor: isUser ? 'rgba(255,255,255,0.1)' : 'var(--color-surface-secondary)',
            color: isUser ? 'white' : 'var(--color-text-primary)',
            padding: '12px',
            borderRadius: '8px',
            overflow: 'auto',
            fontSize: '0.9em',
            fontFamily: 'monospace',
            marginBottom: '0.5em'
          }}
        >
          <code {...props}>{children}</code>
        </pre>
      );
    },
    
    // 引用組件
    blockquote: ({ children, ...props }: any) => (
      <blockquote
        style={{
          borderLeft: `4px solid ${isUser ? 'rgba(255,255,255,0.5)' : 'var(--color-neon-blue)'}`,
          paddingLeft: '12px',
          marginLeft: '0',
          marginBottom: '0.5em',
          fontStyle: 'italic',
          color: isUser ? 'rgba(255,255,255,0.9)' : 'var(--color-text-secondary)'
        }}
        {...props}
      >
        {children}
      </blockquote>
    ),
    
    // 強調組件
    strong: ({ children, ...props }: any) => (
      <strong
        style={{
          color: isUser ? 'white' : 'var(--color-text-primary)',
          fontWeight: 'bold'
        }}
        {...props}
      >
        {children}
      </strong>
    ),
    
    em: ({ children, ...props }: any) => (
      <em
        style={{
          color: isUser ? 'white' : 'var(--color-text-primary)',
          fontStyle: 'italic'
        }}
        {...props}
      >
        {children}
      </em>
    )
  };

  // 如果內容包含 Markdown 語法，使用 ReactMarkdown 渲染
  if (hasMarkdownSyntax(content)) {
    return (
      <div className="enhanced-text-renderer">
        <ReactMarkdown
          remarkPlugins={[remarkGfm]}
          rehypePlugins={[rehypeRaw, rehypeSanitize]}
          components={markdownComponents}
        >
          {content}
        </ReactMarkdown>
      </div>
    );
  }

  // 否則渲染純文本，但處理 URL 連結
  return (
    <div 
      className="enhanced-text-renderer whitespace-pre-wrap"
      style={{
        color: isUser ? 'white' : 'var(--color-text-primary)',
        lineHeight: '1.5'
      }}
    >
      {renderTextWithLinks(content)}
    </div>
  );
};

export default EnhancedTextRenderer;
