/**
 * 消息渲染組件
 * 用於渲染複雜的 AI 回復消息
 */

import React from 'react';
import { Button, Image, Space, Typography, Card, Tag } from 'antd';
import { LinkOutlined, PlayCircleOutlined } from '@ant-design/icons';
import {
  ParsedMessageContent,
  TextMessageContent,
  ImageMessageContent,
  ButtonMessageContent,
  FlexMessageContent,
  QuickReplyItem,
  NewQuickReplyItem
} from '@/types/chat';
import EnhancedTextRenderer from './EnhancedTextRenderer';

const { Text, Paragraph } = Typography;

interface MessageRendererProps {
  parsedContent: ParsedMessageContent;
  isUser?: boolean;
  onQuickReplyClick?: (item: QuickReplyItem) => void;
  onNewQuickReplyClick?: (item: NewQuickReplyItem) => void;
}

const MessageRenderer: React.FC<MessageRendererProps> = ({
  parsedContent,
  isUser = false,
  onQuickReplyClick,
  onNewQuickReplyClick,
}) => {

  // 渲染單個消息內容
  const renderMessageContent = (content: any, index: number) => {
    switch (content.type) {
      case 'Text':
        return renderTextMessage(content, index);
      case 'Image':
        return renderImageMessage(content, index);
      case 'Template':
        return renderButtonMessage(content, index);
      case 'Flex':
        return renderFlexMessage(content, index);
      default:
        return renderUnknownMessage(content, index);
    }
  };

  // 渲染文本消息
  const renderTextMessage = (content: TextMessageContent, index: number) => {
    // 直接顯示文本內容，不使用動畫
    return (
      <div key={index} className="message-text mb-2">
        <EnhancedTextRenderer
          content={content.text}
          isUser={isUser}
        />
      </div>
    );
  };

  // 渲染圖片消息
  const renderImageMessage = (content: ImageMessageContent, index: number) => (
    <div key={index} className="message-image mb-2">
      <Image
        src={content.originalContentUrl}
        preview={{
          src: content.previewImageUrl || content.originalContentUrl,
        }}
        alt="AI 回復圖片"
        style={{ maxWidth: '100%', maxHeight: '300px' }}
      />
    </div>
  );

  // 渲染按鈕消息
  const renderButtonMessage = (content: ButtonMessageContent, index: number) => (
    <div key={index} className="message-buttons mb-2">
      <Card
        size="small"
        className="border-0"
        style={{
          backgroundColor: isUser ? 'var(--color-neon-blue)' : 'var(--color-surface-primary)',
        }}
        bodyStyle={{ padding: '12px' }}
      >
        <Text
          className="block mb-3"
          style={{
            fontSize: '14px',
            color: isUser ? 'white' : 'var(--color-text-primary)'
          }}
        >
          {content.template.text}
        </Text>
        <Space direction="vertical" size="small" className="w-full">
          {content.template.actions.map((action, actionIndex) => (
            <Button
              key={actionIndex}
              type={isUser ? 'default' : 'primary'}
              size="small"
              block
              icon={action.type === 'uri' ? <LinkOutlined /> : undefined}
              onClick={() => handleActionClick(action)}
              style={{
                backgroundColor: isUser ? 'white' : 'var(--color-neon-blue)',
                borderColor: isUser ? 'white' : 'var(--color-neon-blue)',
                color: isUser ? 'var(--color-neon-blue)' : 'white'
              }}
            >
              {action.label}
            </Button>
          ))}
        </Space>
      </Card>
    </div>
  );

  // 渲染 Flex 消息（簡化版本）
  const renderFlexMessage = (content: FlexMessageContent, index: number) => (
    <div key={index} className="message-flex mb-2">
      <Card
        size="small"
        className="border-0"
        style={{
          backgroundColor: isUser ? 'var(--color-neon-blue)' : 'var(--color-surface-primary)',
        }}
        bodyStyle={{ padding: '12px' }}
      >
        <Text
          style={{
            fontSize: '14px',
            color: isUser ? 'white' : 'var(--color-text-primary)'
          }}
        >
          {content.altText}
        </Text>
        <div className="mt-2">
          <Tag
            icon={<PlayCircleOutlined />}
            color={isUser ? 'blue' : 'green'}
            className="text-xs"
          >
            Flex 消息
          </Tag>
        </div>
      </Card>
    </div>
  );

  // 渲染未知消息類型
  const renderUnknownMessage = (content: any, index: number) => (
    <div key={index} className="message-unknown mb-2">
      <Text
        className="text-sm"
        style={{ color: 'var(--color-text-secondary)' }}
      >
        Flex 消息替代文字: {content.altText || '未知消息類型'}
      </Text>
    </div>
  );

  // 處理按鈕點擊
  const handleActionClick = (action: any) => {
    switch (action.type) {
      case 'message':
        // 發送消息
        if (onQuickReplyClick) {
          onQuickReplyClick({
            type: 'action',
            action: {
              type: 'message',
              label: action.label,
              text: action.text || action.label
            }
          });
        }
        break;
      case 'uri':
        // 打開鏈接
        if (action.uri) {
          window.open(action.uri, '_blank');
        }
        break;
      case 'postback':
        // 處理 postback
        if (onQuickReplyClick) {
          onQuickReplyClick({
            type: 'action',
            action: {
              type: 'postback',
              label: action.label,
              data: action.data || action.label
            }
          });
        }
        break;
      default:
        console.warn('Unknown action type:', action.type);
    }
  };

  // 渲染快速回復按鈕
  const renderQuickReply = () => {
    const hasOldQuickReply = parsedContent.quickReplyItems && parsedContent.quickReplyItems.length > 0;
    const hasNewQuickReply = parsedContent.newQuickReplyItems && parsedContent.newQuickReplyItems.length > 0;

    if (!parsedContent.hasQuickReply || (!hasOldQuickReply && !hasNewQuickReply)) {
      return null;
    }

    return (
      <div className="quick-reply-container mt-3 pt-3 border-t border-opacity-20"
           style={{ borderColor: 'var(--color-border-primary)' }}>
        <Text
          className="text-xs mb-2 block"
          style={{ color: isUser ? 'rgba(255,255,255,0.8)' : 'var(--color-text-tertiary)' }}
        >
          快速回復：
        </Text>
        <Space wrap size="small">
          {/* 渲染舊格式的快速回復按鈕 */}
          {hasOldQuickReply && parsedContent.quickReplyItems.map((item, index) => {
            // 支援多種數據格式：優先使用 label，然後是 text，最後是 data
            const buttonText = item?.action?.label || item?.action?.text || item?.action?.data || '快速回復';
            return (
              <Button
                key={`old-${index}`}
                size="small"
                type="default"
                onClick={() => onQuickReplyClick?.(item)}
                className="text-xs quick-reply-btn"
                style={{
                  backgroundColor: isUser
                    ? 'var(--color-surface-secondary)'
                    : 'var(--color-surface-primary)',
                  borderColor: 'var(--color-border-primary)',
                  color: 'var(--color-text-primary)',
                  transition: 'all 0.2s ease',
                  fontWeight: 500
                }}
              >
                {buttonText}
              </Button>
            );
          })}

          {/* 渲染新格式的快速回復按鈕 */}
          {hasNewQuickReply && parsedContent.newQuickReplyItems!.map((item, index) => {
            // 新格式使用 title 字段
            const buttonText = item?.title || '快速回復';
            return (
              <Button
                key={`new-${index}`}
                size="small"
                type="default"
                onClick={() => onNewQuickReplyClick?.(item)}
                className="text-xs quick-reply-btn"
                style={{
                  backgroundColor: isUser
                    ? 'var(--color-surface-secondary)'
                    : 'var(--color-surface-primary)',
                  borderColor: 'var(--color-border-primary)',
                  color: 'var(--color-text-primary)',
                  transition: 'all 0.2s ease',
                  fontWeight: 500
                }}
              >
                {buttonText}
              </Button>
            );
          })}
        </Space>
      </div>
    );
  };

  return (
    <div className="message-renderer">
      {/* 渲染所有消息內容 */}
      {parsedContent.contents.map((content, index) =>
        renderMessageContent(content, index)
      )}

      {/* 渲染快速回復按鈕 */}
      {renderQuickReply()}
    </div>
  );
};

export default MessageRenderer;
