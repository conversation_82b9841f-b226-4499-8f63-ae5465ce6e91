/**
 * 聊天面板組件
 */

import React, { useState, useRef, useEffect } from 'react';
import { Layout, Button, Typography, Space, Input, Card, Avatar, message, Tag, Upload } from 'antd';
import {
  PlusOutlined,
  UserOutlined,
  PaperClipOutlined,
  LoadingOutlined
} from '@ant-design/icons';
import {
  MdDelete,
  MdSend,
  MdSmartToy
} from 'react-icons/md';
import type { UploadProps } from 'antd';
import { useAppStore } from '@/hooks/useAppStore';
import { useChatService } from '@/services/chatService';
import { MessageParser, hasAttachmentReference, extractQuickReplyTexts } from '@/utils/messageParser';
import { ChatMessage } from '@/types/chat';
import MessageRenderer from './MessageRenderer';
import ResponseTimer from './ResponseTimer';
import { SpotlightCard, AnimatedText } from '@/components/ReactBits';
import { useResponseTimer } from '@/hooks/useResponseTimer';

const { Header, Content } = Layout;
const { Title, Text } = Typography;
const { TextArea } = Input;

const ChatPanel: React.FC = () => {
  const {
    currentSession,
    userInfo,
    attachments,
    chatLoading,
    setCurrentSession,
    addMessageToCurrentSession,
    updateLastMessage,
    createNewSession,
    setChatLoading,
  } = useAppStore();

  const chatService = useChatService();
  const [inputValue, setInputValue] = useState('');
  const [uploadingFile, setUploadingFile] = useState<File | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // 計時器Hook
  const {
    elapsedTime,
    isRunning: timerIsRunning,
    currentMessageId,
    startWaitingTimer,
    stopTimer,
    resetTimer,
  } = useResponseTimer();

  // 自動滾動到底部
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [currentSession?.messages]);

  // 檢查是否有用戶信息
  const hasUserInfo = userInfo.tenant_id && userInfo.tenant_id.trim() !== '';

  // 創建新對話
  const handleNewChat = () => {
    if (!hasUserInfo) {
      message.warning('請先設置正確的 URL 參數');
      return;
    }
    createNewSession();
  };

  // 清空當前對話
  const handleClearChat = () => {
    if (currentSession && currentSession.messages.length > 0) {
      handleNewChat();
    }
  };



  // 發送消息
  const handleSendMessage = async (messageText?: string) => {
    // 用戶發送消息時開始計時
    startWaitingTimer();

    // 使用傳入的文本或輸入框的值，確保是字符串類型
    let textToSend: string;
    if (messageText !== undefined) {
      // 如果傳入了 messageText，確保它是字符串
      textToSend = typeof messageText === 'string' ? messageText : String(messageText);
    } else {
      // 否則使用輸入框的值
      textToSend = inputValue || '';
    }

    if (!textToSend.trim() || !currentSession || !hasUserInfo) {
      if (!hasUserInfo) {
        message.warning('請先設置正確的 URL 參數');
      }
      // 如果沒有發送消息，停止計時器
      resetTimer();
      return;
    }

    const question = textToSend.trim();
    const userMessage: ChatMessage = {
      id: chatService.generateMessageId(),
      type: 'user',
      content: question,
      timestamp: new Date().toISOString(),
    };

    // 添加用戶消息
    addMessageToCurrentSession(userMessage);
    setInputValue('');
    setChatLoading(true);

    try {
      // 構建請求
      let request;
      if (uploadingFile) {
        // 附件聊天
        request = chatService.buildOmnichannelAttachmentRequest(
          question,
          uploadingFile,
          userInfo,
          currentSession.id
        );
      } else {
        // 普通聊天，檢查是否有附件作為知識背景
        const hasAttachments = attachments.length > 0;
        request = chatService.buildOmnichannelRequest(
          question,
          userInfo,
          currentSession.id,
          hasAttachments
        );
      }

      // 調用 AI API
      const response = await chatService.omnichannelChat(request);

      if (response.code === 0 && response.answer) {
        // 處理 answer 字段 - 可能是字符串、數組或對象
        let answerString: string;
        let parsedContent: any;

        if (typeof response.answer === 'string') {
          // 如果是字符串，直接使用
          answerString = response.answer;
          parsedContent = MessageParser.parseMessage(answerString);
        } else if (Array.isArray(response.answer)) {
          // 如果是數組，直接解析為消息內容
          parsedContent = MessageParser.parseMessageArray(response.answer);
          answerString = JSON.stringify(response.answer);
        } else {
          // 如果是對象，轉換為字符串
          answerString = JSON.stringify(response.answer);
          parsedContent = MessageParser.parseMessage(answerString);
        }

        const aiMessage: ChatMessage = {
          id: chatService.generateMessageId(),
          type: 'assistant',
          content: answerString,
          parsedContent,
          timestamp: new Date().toISOString(),
        };

        // AI回復收到時停止計時器
        stopTimer(aiMessage.id);

        addMessageToCurrentSession(aiMessage);

        // 如果回復中引用了附件，顯示提示
        if (hasAttachmentReference(response.answer)) {
          message.success('AI 已基於您的附件內容回答');
        }
      } else {
        throw new Error(response.message || 'AI 回復失敗');
      }
    } catch (error) {
      console.error('發送消息失敗:', error);

      const errorMessage: ChatMessage = {
        id: chatService.generateMessageId(),
        type: 'assistant',
        content: `抱歉，發生了錯誤：${error instanceof Error ? error.message : '未知錯誤'}`,
        timestamp: new Date().toISOString(),
      };

      addMessageToCurrentSession(errorMessage);
      message.error('發送消息失敗');
    } finally {
      setChatLoading(false);
      setUploadingFile(null);
    }
  };

  // 處理鍵盤事件
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // 處理快速回復（舊格式）
  const handleQuickReplyClick = (item: any) => {
    const text = item?.action?.text || item?.action?.label || item?.action?.title || item?.text || item?.label || item?.title;
    if (text && typeof text === 'string' && text.trim()) {
      // 設置輸入框顯示文本
      setInputValue(text);
      // 直接發送消息
      handleSendMessage(text);
    }
  };

  // 處理快速回復（新格式）
  const handleNewQuickReplyClick = (item: any) => {
    const text = item?.displayText || item?.title || item?.text || item?.label;
    if (text && typeof text === 'string' && text.trim()) {
      // 設置輸入框顯示文本
      setInputValue(text);
      // 直接發送消息
      handleSendMessage(text);
    }
  };

  // 處理文件上傳
  const handleFileUpload = (file: File) => {
    setUploadingFile(file);
    message.success(`已選擇文件：${file.name}`);
    return false; // 阻止自動上傳
  };

  // 移除上傳的文件
  const handleRemoveFile = () => {
    setUploadingFile(null);
  };

  // 上傳配置
  const uploadProps: UploadProps = {
    beforeUpload: handleFileUpload,
    showUploadList: false,
    accept: '.pdf,.doc,.docx,.txt,.jpg,.jpeg,.png,.gif',
  };

  return (
    <Layout className="h-full transition-colors duration-300" style={{ background: 'var(--color-bg-secondary)' }}>
      {/* 聊天頭部 */}
      <Header
        className="px-6 flex items-center justify-between transition-colors duration-300"
        style={{
          background: 'var(--color-surface-primary)',
          borderBottom: '1px solid var(--color-border-primary)',
          height: '64px'
        }}
      >
        <div className="flex items-center space-x-4">
          <Title level={4} className="!mb-0" style={{ color: 'var(--color-text-primary)' }}>
            💬 智慧聊天
          </Title>
          {currentSession && (
            <Space>
              <Text className="text-sm" style={{ color: 'var(--color-text-secondary)' }}>
                會話 ID: {currentSession.id.slice(-8)}
              </Text>
              {attachments.length > 0 && (
                <Tag color="blue" className="text-xs">
                  {attachments.length} 個附件作為知識背景
                </Tag>
              )}
            </Space>
          )}
        </div>

        <Space>
          <Button
            type="default"
            icon={<PlusOutlined />}
            onClick={handleNewChat}
            disabled={!hasUserInfo}
            style={{
              borderColor: 'var(--color-neon-blue)',
              color: 'var(--color-neon-blue)'
            }}
          >
            新對話
          </Button>
          {currentSession && currentSession.messages.length > 0 && (
            <Button
              type="default"
              icon={<MdDelete />}
              onClick={handleClearChat}
            >
              清空對話
            </Button>
          )}
        </Space>
      </Header>

      {/* 聊天內容區域 */}
      <Content className="flex flex-col transition-colors duration-300" style={{ background: 'var(--color-bg-secondary)' }}>
        {/* 消息列表 */}
        <div className="flex-1 overflow-y-auto p-4">
          {!hasUserInfo ? (
            <div className="h-full flex items-center justify-center">
              <div className="text-center space-y-4">
                <div className="text-6xl">⚠️</div>
                <Title level={3} style={{ color: 'var(--color-text-primary)' }}>
                  需要設置 URL 參數
                </Title>
                <Text style={{ color: 'var(--color-text-secondary)' }} className="block">
                  請在 URL 中添加必要的參數：<br />
                  ?TenantID=your_tenant&ServiceID=your_service&UserID=your_user
                </Text>
              </div>
            </div>
          ) : !currentSession ? (
            <div className="h-full flex items-center justify-center">
              <div className="text-center space-y-4">
                <div className="text-6xl">💬</div>
                <AnimatedText
                  text="歡迎使用 KM Client"
                  animation="slideUp"
                  duration={0.8}
                  as="h3"
                  className="text-2xl font-semibold"
                  style={{ color: 'var(--color-text-primary)' }}
                />
                <AnimatedText
                  text={`點擊右上角"新對話"按鈕開始與 AI 助手交流${
                    attachments.length > 0
                      ? `\n已載入 ${attachments.length} 個附件作為知識背景`
                      : ''
                  }`}
                  animation="fadeIn"
                  duration={1}
                  delay={0.3}
                  as="p"
                  className="whitespace-pre-line"
                  style={{ color: 'var(--color-text-secondary)' }}
                />
              </div>
            </div>
          ) : currentSession.messages.length === 0 ? (
            <div className="h-full flex items-center justify-center">
              <div className="text-center space-y-4">
                <div className="text-4xl">🤖</div>
                <Title level={4} style={{ color: 'var(--color-text-primary)' }}>
                  開始新的對話
                </Title>
                <Text style={{ color: 'var(--color-text-secondary)' }}>
                  在下方輸入框中輸入您的問題，AI 助手將為您提供幫助
                </Text>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              {currentSession.messages.map((message, index) => (
                <div
                  key={message.id}
                  className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
                >
                  <div className={`flex items-start space-x-3 max-w-[80%]`}>
                    {message.type === 'assistant' && (
                      <Avatar
                        icon={<MdSmartToy />}
                        style={{ background: 'var(--color-neon-blue)' }}
                      />
                    )}
                    <SpotlightCard
                      className="transition-colors duration-300"
                      backgroundColor={
                        message.type === 'user'
                          ? 'var(--color-neon-blue)'
                          : 'var(--color-surface-primary)'
                      }
                      spotlightColor={
                        message.type === 'user'
                          ? 'rgba(255, 255, 255, 0.2)'
                          : 'rgba(167, 139, 250, 0.3)'
                      }
                      padding="12px 16px"
                      borderRadius={12}
                      disabled={message.type === 'user'} // 用戶消息不需要光效
                      style={{
                        border: message.type === 'user'
                          ? 'none'
                          : '1px solid var(--color-border-primary)',
                        color: message.type === 'user'
                          ? 'white'
                          : 'var(--color-text-primary)'
                      }}
                    >
                      {message.parsedContent ? (
                        <MessageRenderer
                          parsedContent={message.parsedContent}
                          isUser={message.type === 'user'}
                          onQuickReplyClick={handleQuickReplyClick}
                          onNewQuickReplyClick={handleNewQuickReplyClick}
                        />
                      ) : (
                        <div className="whitespace-pre-wrap">{message.content}</div>
                      )}
                      <div className="text-xs mt-2 opacity-70">
                        {new Date(message.timestamp).toLocaleTimeString()}
                      </div>
                    </SpotlightCard>
                    {message.type === 'user' && (
                      <Avatar
                        icon={<UserOutlined />}
                        style={{ background: 'var(--color-text-tertiary)' }}
                      />
                    )}
                  </div>
                </div>
              ))}
              {chatLoading && (
                <div className="flex justify-start">
                  <div className="flex items-start space-x-3">
                    <div className="flex items-center space-x-2">
                      <Avatar
                        icon={<MdSmartToy />}
                        style={{ background: 'var(--color-neon-blue)' }}
                      />
                      {/* 計時器顯示在機器人頭像後面 */}
                      {timerIsRunning && currentMessageId === 'waiting' && (
                        <div className="text-xs text-gray-500">
                          <ResponseTimer
                            elapsedTime={elapsedTime}
                            isRunning={true}
                          />
                        </div>
                      )}
                    </div>
                    <SpotlightCard
                      className="transition-colors duration-300"
                      backgroundColor="var(--color-surface-primary)"
                      spotlightColor="rgba(167, 139, 250, 0.3)"
                      padding="12px 16px"
                      borderRadius={12}
                      style={{
                        border: '1px solid var(--color-border-primary)',
                        color: 'var(--color-text-primary)'
                      }}
                    >
                      <div className="flex items-center space-x-2">
                        <AnimatedText
                          text="AI 正在思考中..."
                          animation="typewriter"
                          duration={2}
                          repeat={true}
                        />
                      </div>
                    </SpotlightCard>
                  </div>
                </div>
              )}
              <div ref={messagesEndRef} />
            </div>
          )}
        </div>

        {/* 輸入區域 */}
        {currentSession && hasUserInfo && (
          <div
            className="border-t p-4 transition-colors duration-300"
            style={{ borderTop: '1px solid var(--color-border-primary)' }}
          >
            {/* 文件上傳提示 */}
            {uploadingFile && (
              <div className="mb-3 p-2 rounded-lg transition-colors duration-300"
                   style={{
                     background: 'var(--color-bg-tertiary)',
                     border: '1px solid var(--color-border-secondary)'
                   }}>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <PaperClipOutlined style={{ color: 'var(--color-neon-blue)' }} />
                    <Text className="text-sm" style={{ color: 'var(--color-text-primary)' }}>
                      已選擇文件：{uploadingFile.name}
                    </Text>
                  </div>
                  <Button
                    type="text"
                    size="small"
                    onClick={handleRemoveFile}
                    style={{ color: 'var(--color-text-tertiary)' }}
                  >
                    移除
                  </Button>
                </div>
              </div>
            )}

            <div className="flex space-x-2">
              <div className="flex-1 flex space-x-2">
                <TextArea
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="輸入您的問題..."
                  autoSize={{ minRows: 1, maxRows: 4 }}
                  disabled={chatLoading}
                  className="flex-1 transition-colors duration-300"
                  style={{
                    background: 'var(--color-surface-primary)',
                    border: '1px solid var(--color-border-primary)',
                    color: 'var(--color-text-primary)'
                  }}
                />
                <Upload {...uploadProps}>
                  <Button
                    type="default"
                    icon={<PaperClipOutlined />}
                    disabled={chatLoading}
                    style={{
                      borderColor: 'var(--color-light-300)',
                      height: 'auto',
                      minHeight: '32px'
                    }}
                  />
                </Upload>
              </div>
              <Button
                type="primary"
                icon={chatLoading ? <LoadingOutlined /> : <MdSend />}
                onClick={() => handleSendMessage()}
                loading={chatLoading}
                disabled={!inputValue.trim()}
                style={{ background: 'var(--color-neon-blue)' }}
              >
                {chatLoading ? '發送中' : '發送'}
              </Button>
            </div>
          </div>
        )}
      </Content>
    </Layout>
  );
};

export default ChatPanel;
