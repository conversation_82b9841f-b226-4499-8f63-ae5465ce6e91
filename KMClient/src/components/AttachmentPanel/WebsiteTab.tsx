/**
 * 網站管理標籤頁組件
 */

import React, { useState } from 'react';
import { Button, List, Space, Popconfirm, Typography, Empty, Modal, Form, Input, Tooltip, Alert, message } from 'antd';
import {
  PlusOutlined,
  LoadingOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import {
  MdDelete,
  MdLanguage,
  MdOpenInNew,
  MdRefresh,
  MdSkipNext
} from 'react-icons/md';
import { useAppStore } from '@/hooks/useAppStore';
import { useAttachmentWithRefresh } from '@/hooks/useAttachmentWithRefresh';
import { AttachmentType, WebsiteAttachment } from '@/types/attachment';
import { SpotlightCard } from '@/components/ReactBits';

const { Text } = Typography;

interface WebsiteFormData {
  url: string;
  remark: string;
}

interface ValidationState {
  isValidating: boolean;
  validationError: string | null;
  validationSuccess: boolean;
  showSkipOption: boolean;
}

const WebsiteTab: React.FC = () => {
  const { attachments, userInfo, attachmentsLoading } = useAppStore();
  const { setWebsite, deleteWebsite } = useAttachmentWithRefresh();
  const [modalVisible, setModalVisible] = useState(false);
  const [form] = Form.useForm<WebsiteFormData>();
  const [validationState, setValidationState] = useState<ValidationState>({
    isValidating: false,
    validationError: null,
    validationSuccess: false,
    showSkipOption: false,
  });

  // 獲取網站附件
  const websiteAttachments = attachments.filter(att => att.type === AttachmentType.WEBSITE) as WebsiteAttachment[];

  // 網站可訪問性驗證
  const validateWebsiteAccess = async (url: string): Promise<{ success: boolean; error?: string }> => {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 8000); // 8秒超時

    try {
      // 確保URL格式正確
      const normalizedUrl = url.startsWith('http') ? url : `https://${url}`;

      // 嘗試使用 no-cors 模式進行基本連通性測試
      const response = await fetch(normalizedUrl, {
        method: 'HEAD',
        mode: 'no-cors',
        signal: controller.signal,
        cache: 'no-cache',
      });

      clearTimeout(timeoutId);

      // no-cors 模式下，成功的請求會返回 opaque 響應
      // 如果沒有拋出錯誤，說明網站基本可訪問
      return { success: true };

    } catch (error: any) {
      clearTimeout(timeoutId);

      if (error.name === 'AbortError') {
        return {
          success: false,
          error: '網站響應超時，請檢查URL是否正確或網站是否可訪問'
        };
      }

      if (error.name === 'TypeError' && error.message.includes('Failed to fetch')) {
        // 嘗試備選驗證方法：使用圖片加載測試
        try {
          await testWebsiteWithImage(url);
          return { success: true };
        } catch {
          return {
            success: false,
            error: '無法連接到該網站，請檢查URL是否正確或網站是否可訪問'
          };
        }
      }

      return {
        success: false,
        error: '網站訪問異常，請檢查URL是否正確或網站是否可訪問'
      };
    }
  };

  // 備選驗證方法：使用圖片加載測試網站連通性
  const testWebsiteWithImage = (url: string): Promise<void> => {
    return new Promise((resolve, reject) => {
      const img = new Image();
      const timeout = setTimeout(() => {
        reject(new Error('Timeout'));
      }, 5000);

      img.onload = () => {
        clearTimeout(timeout);
        resolve();
      };

      img.onerror = () => {
        clearTimeout(timeout);
        reject(new Error('Failed to load'));
      };

      // 嘗試加載網站的 favicon
      const normalizedUrl = url.startsWith('http') ? url : `https://${url}`;
      const faviconUrl = new URL('/favicon.ico', normalizedUrl).href;
      img.src = faviconUrl;
    });
  };

  // 處理添加網站
  const handleAddWebsite = () => {
    setModalVisible(true);
    resetValidationState();
  };

  // 處理表單提交（帶驗證）
  const handleSubmit = async (values: WebsiteFormData) => {
    if (!userInfo.tenant_id) {
      console.error('缺少用戶信息');
      return;
    }

    // 重置驗證狀態
    setValidationState({
      isValidating: true,
      validationError: null,
      validationSuccess: false,
      showSkipOption: false,
    });

    try {
      // 執行網站可訪問性驗證
      const validationResult = await validateWebsiteAccess(values.url);

      if (validationResult.success) {
        // 驗證成功，繼續添加網站
        setValidationState(prev => ({
          ...prev,
          isValidating: false,
          validationSuccess: true,
        }));

        await addWebsiteToBackend(values);

      } else {
        // 驗證失敗，顯示錯誤和跳過選項
        setValidationState({
          isValidating: false,
          validationError: validationResult.error || '網站驗證失敗',
          validationSuccess: false,
          showSkipOption: true,
        });
      }
    } catch (error) {
      console.error('網站驗證過程出錯:', error);
      setValidationState({
        isValidating: false,
        validationError: '驗證過程出現錯誤，請重試',
        validationSuccess: false,
        showSkipOption: true,
      });
    }
  };

  // 實際添加網站到後端
  const addWebsiteToBackend = async (values: WebsiteFormData) => {
    try {
      await setWebsite({
        tenant_id: userInfo.tenant_id,
        service_id: userInfo.service_id,
        user_id: userInfo.user_id,
        web_sites: [{
          url: values.url,
          remark: values.remark,
        }],
      });

      message.success('網站添加成功！');
      setModalVisible(false);
      form.resetFields();
      resetValidationState();
    } catch (error) {
      console.error('添加網站失敗:', error);
      message.error('添加網站失敗，請重試');
    }
  };

  // 跳過驗證直接添加
  const handleSkipValidation = async () => {
    const values = form.getFieldsValue();
    await addWebsiteToBackend(values);
  };

  // 重試驗證
  const handleRetryValidation = () => {
    const values = form.getFieldsValue();
    handleSubmit(values);
  };

  // 重置驗證狀態
  const resetValidationState = () => {
    setValidationState({
      isValidating: false,
      validationError: null,
      validationSuccess: false,
      showSkipOption: false,
    });
  };

  // 處理刪除網站
  const handleDelete = async (url: string) => {
    if (!userInfo.tenant_id) {
      console.error('缺少用戶信息');
      return;
    }

    try {
      await deleteWebsite({
        tenant_id: userInfo.tenant_id,
        service_id: userInfo.service_id,
        user_id: userInfo.user_id,
        web_site: [url], // 修正：使用 web_site 而不是 web_sites，直接傳遞 URL 字符串
      });
    } catch (error) {
      console.error('刪除網站失敗:', error);
    }
  };

  // 處理取消
  const handleCancel = () => {
    setModalVisible(false);
    form.resetFields();
    resetValidationState();
  };

  // 驗證 URL 格式
  const validateUrl = (_: any, value: string) => {
    if (!value) {
      return Promise.reject(new Error('請輸入網站 URL'));
    }
    
    try {
      new URL(value);
      return Promise.resolve();
    } catch {
      return Promise.reject(new Error('請輸入有效的 URL 格式'));
    }
  };

  return (
    <div className="space-y-4">
      {/* 添加按鈕 */}
      <Button 
        type="primary" 
        icon={<PlusOutlined />} 
        onClick={handleAddWebsite}
        block
        data-testid="add-button"
        style={{ background: 'var(--color-neon-blue)' }}
        disabled={!userInfo.tenant_id}
      >
        添加網站連結
      </Button>

      {/* 網站列表 */}
      {websiteAttachments.length === 0 ? (
        <Empty
          description="暫無網站附件"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          style={{ color: 'var(--color-text-secondary)' }}
        />
      ) : (
        <List
          loading={attachmentsLoading}
          dataSource={websiteAttachments}
          renderItem={(item) => (
            <SpotlightCard
              className="mb-2"
              backgroundColor="var(--color-surface-primary)"
              spotlightColor="rgba(167, 139, 250, 0.2)"
              padding="16px"
              borderRadius={8}
            >
              <List.Item
                className="!p-0 !border-0"
                actions={[
                <Button
                  key="visit"
                  type="text"
                  icon={<MdOpenInNew />}
                  onClick={() => window.open(item.url, '_blank')}
                  style={{ color: 'var(--color-neon-blue)' }}
                />,
                <Popconfirm
                  key="delete"
                  title="確定要刪除這個網站嗎？"
                  onConfirm={() => handleDelete(item.url)}
                  okText="確定"
                  cancelText="取消"
                >
                  <Button
                    type="text"
                    icon={<MdDelete />}
                    danger
                  />
                </Popconfirm>,
              ]}
            >
              <List.Item.Meta
                avatar={<MdLanguage style={{ color: 'var(--color-neon-blue)', fontSize: '20px' }} />}
                title={
                  <Text strong style={{ color: 'var(--color-text-primary)' }}>
                    {item.remark || item.url}
                  </Text>
                }
                description={
                  <Space direction="vertical" size="small">
                    {/* URL 顯示，如果有 remark 則顯示懸浮提示 */}
                    {item.remark ? (
                      <Tooltip title={item.remark} placement="topLeft">
                        <Text
                          className="text-sm break-all cursor-pointer hover:text-blue-500"
                          style={{ color: 'var(--color-neon-blue)' }}
                          onClick={() => window.open(item.url, '_blank')}
                        >
                          {item.url}
                        </Text>
                      </Tooltip>
                    ) : (
                      <Text
                        className="text-sm break-all cursor-pointer hover:text-blue-500"
                        style={{ color: 'var(--color-neon-blue)' }}
                        onClick={() => window.open(item.url, '_blank')}
                      >
                        {item.url}
                      </Text>
                    )}
                    <Text className="text-xs" style={{ color: 'var(--color-text-tertiary)' }}>
                      添加時間: {new Date(item.created_at).toLocaleString()}
                    </Text>
                  </Space>
                }
              />
            </List.Item>
            </SpotlightCard>
          )}
        />
      )}

      {/* 添加網站模態框 */}
      <Modal
        title={
          <Space>
            <MdLanguage style={{ color: 'var(--color-neon-blue)' }} />
            添加網站連結
            {validationState.isValidating && <LoadingOutlined style={{ color: 'var(--color-neon-blue)' }} />}
            {validationState.validationSuccess && <CheckCircleOutlined style={{ color: '#52c41a' }} />}
          </Space>
        }
        open={modalVisible}
        onCancel={handleCancel}
        footer={null}
        width={500}
        maskClosable={!validationState.isValidating}
        closable={!validationState.isValidating}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          autoComplete="off"
        >
          <Form.Item
            label="網站 URL"
            name="url"
            rules={[
              { validator: validateUrl }
            ]}
          >
            <Input
              placeholder="https://example.com"
              prefix={<MdLanguage />}
            />
          </Form.Item>

          <Form.Item
            label="備註說明"
            name="remark"
            rules={[
              { required: true, message: '請輸入備註說明' }
            ]}
          >
            <Input.TextArea
              placeholder="請輸入網站的描述或用途說明"
              rows={3}
            />
          </Form.Item>

          {/* 驗證狀態顯示 */}
          {validationState.isValidating && (
            <Alert
              message="正在驗證網站可訪問性..."
              description="請稍候，我們正在檢查網站是否可以正常訪問"
              type="info"
              icon={<LoadingOutlined />}
              showIcon
              className="mb-4"
            />
          )}

          {validationState.validationSuccess && (
            <Alert
              message="網站驗證成功！"
              description="網站可以正常訪問，正在添加到附件列表..."
              type="success"
              icon={<CheckCircleOutlined />}
              showIcon
              className="mb-4"
            />
          )}

          {validationState.validationError && (
            <Alert
              message="網站驗證失敗"
              description={validationState.validationError}
              type="warning"
              icon={<ExclamationCircleOutlined />}
              showIcon
              className="mb-4"
              action={
                <Space direction="vertical" size="small">
                  <Button
                    size="small"
                    icon={<MdRefresh />}
                    onClick={handleRetryValidation}
                    loading={validationState.isValidating}
                  >
                    重試驗證
                  </Button>
                  {validationState.showSkipOption && (
                    <Button
                      size="small"
                      type="link"
                      icon={<MdSkipNext />}
                      onClick={handleSkipValidation}
                      style={{ color: 'var(--color-text-secondary)' }}
                    >
                      跳過驗證並添加
                    </Button>
                  )}
                </Space>
              }
            />
          )}

          <Form.Item className="mb-0">
            <Space className="w-full justify-end">
              <Button onClick={handleCancel}>
                取消
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={validationState.isValidating}
                disabled={validationState.isValidating}
                style={{ background: 'var(--color-neon-blue)' }}
              >
                {validationState.isValidating ? '驗證中...' : '添加'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default WebsiteTab;
