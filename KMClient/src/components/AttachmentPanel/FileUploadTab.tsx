/**
 * 檔案上傳標籤頁組件
 */

import React, { useState } from 'react';
import { Upload, Button, List, Space, Popconfirm, Typography, Empty, Progress, message, Modal } from 'antd';
import {
  InboxOutlined,
  PlusOutlined
} from '@ant-design/icons';
import {
  MdDownload,
  MdDelete,
  MdInsertDriveFile
} from 'react-icons/md';
import type { UploadProps, UploadFile } from 'antd';
import { useAppStore } from '@/hooks/useAppStore';
import { useAttachmentWithRefresh } from '@/hooks/useAttachmentWithRefresh';
import { AttachmentType, FileAttachment } from '@/types/attachment';
import { generateContentType } from '@/utils/fileUploadUtils';
import { SpotlightCard } from '@/components/ReactBits';

const { Dragger } = Upload;
const { Text } = Typography;

const FileUploadTab: React.FC = () => {
  const { attachments, userInfo, attachmentsLoading } = useAppStore();
  const { uploadFiles, deleteFiles, downloadFile } = useAttachmentWithRefresh();
  const [uploading, setUploading] = useState(false);
  const [fileList, setFileList] = useState<UploadFile[]>([]);

  // 獲取檔案附件
  const fileAttachments = attachments.filter(att => att.type === AttachmentType.FILE) as FileAttachment[];

  // 調試信息
  console.log('📁 FileUploadTab 渲染 - 所有附件:', attachments);
  console.log('📁 FileUploadTab 渲染 - 檔案附件:', fileAttachments);
  console.log('📁 FileUploadTab 渲染 - 附件類型檢查:', attachments.map(att => ({ id: att.id, name: att.name, type: att.type })));

  // 統一的文件上傳處理函數
  const handleUpload = async () => {
    if (!userInfo.tenant_id) {
      console.error('缺少用戶信息');
      return;
    }

    if (fileList.length === 0) {
      console.warn('沒有文件可上傳');
      return;
    }

    setUploading(true);

    // 更新文件狀態為上傳中
    setFileList(prev => prev.map(file => ({
      ...file,
      status: 'uploading' as const
    })));

    try {
      // 從 fileList 中提取實際的 File 對象
      const files = fileList.map(file => file.originFileObj as File);

      console.log('📤 準備上傳文件:', {
        fileCount: files.length,
        fileNames: files.map(f => f.name),
        fileSizes: files.map(f => `${(f.size / 1024).toFixed(2)} KB`),
      });

      await uploadFiles({
        file: files,
        tenant_id: userInfo.tenant_id,
        service_id: userInfo.service_id,
        user_id: userInfo.user_id,
        content_type: generateContentType(files), // 使用統一的 generateContentType 函數
      });

      console.log('✅ 文件上傳成功完成');

      // 更新文件狀態為成功
      setFileList(prev => prev.map(file => ({
        ...file,
        status: 'done' as const
      })));

      // 延遲清空文件列表，讓用戶看到成功狀態
      setTimeout(() => {
        setFileList([]);
        console.log('🧹 文件列表已清空，準備下次上傳');
      }, 1500);

    } catch (error) {
      console.error('❌ 文件上傳失敗:', error);

      // 更新文件狀態為錯誤
      setFileList(prev => prev.map(file => ({
        ...file,
        status: 'error' as const
      })));
    } finally {
      setUploading(false);
    }
  };



  // 處理檔案刪除
  const handleDelete = async (fileName: string) => {
    if (!userInfo.tenant_id) {
      console.error('缺少用戶信息');
      return;
    }

    try {
      await deleteFiles({
        tenant_id: userInfo.tenant_id,
        service_id: userInfo.service_id,
        user_id: userInfo.user_id,
        file_names: [fileName],
      });
    } catch (error) {
      console.error('刪除失敗:', error);
    }
  };

  // 處理檔案下載 - 包含文件完整性預檢查
  const handleDownload = async (attachment: FileAttachment) => {
    try {
      console.log('📥 準備下載文件:', {
        name: attachment.name,
        file_size: attachment.file_size,
        access_path: attachment.access_path,
        access_url: attachment.access_url,
        file_path: attachment.file_path
      });

      // 文件完整性預檢查
      if (attachment.file_size !== undefined && attachment.file_size < 100) {
        console.warn('⚠️ 檢測到異常小的文件:', attachment.name, attachment.file_size, 'bytes');

        // 顯示警告信息
        message.warning({
          content: `文件 "${attachment.name}" 大小異常小（${attachment.file_size} bytes），可能已損壞。建議重新上傳此文件。`,
          duration: 8,
        });

        // 仍然嘗試下載，但會在 API 層面進行更詳細的錯誤處理
        console.log('⚠️ 繼續嘗試下載異常小的文件，但會進行額外的錯誤處理');
      }

      // 確定 file_path 的值：優先使用 access_path，如果為空則使用 access_url，最後使用 file_path
      let filePath = '';

      if (attachment.access_path && attachment.access_path.trim() !== '') {
        filePath = attachment.access_path;
        console.log('✅ 使用 access_path:', filePath);
      } else if (attachment.access_url && attachment.access_url.trim() !== '') {
        filePath = attachment.access_url;
        console.log('✅ access_path 為空，使用 access_url:', filePath);
      } else if (attachment.file_path && attachment.file_path.trim() !== '') {
        filePath = attachment.file_path;
        console.log('✅ access_path 和 access_url 都為空，使用 file_path:', filePath);
      } else {
        throw new Error('無法確定文件路徑：access_path、access_url 和 file_path 都為空');
      }

      // 正常大小的文件，直接下載
      await downloadFile({
        file_path: filePath,
      }, attachment.name);

    } catch (error) {
      console.error('❌ 下載失敗:', error);
      handleDownloadError(error);
    }
  };

  // 處理下載錯誤的統一方法
  const handleDownloadError = (error: any) => {
    // 根據錯誤類型提供不同的用戶反饋
    let errorMessage = '下載文件失敗';

    if (error instanceof Error) {
      errorMessage = error.message;

      // 對於文件損壞的情況，提供額外的建議
      if (error.message.includes('損壞') || error.message.includes('異常小')) {
        Modal.error({
          title: '下載失敗',
          content: (
            <div>
              <p>{error.message}</p>
              <p><strong>建議解決方案：</strong></p>
              <ul style={{ paddingLeft: '20px', marginTop: '10px' }}>
                <li>重新上傳此文件</li>
                <li>檢查原始文件是否完整</li>
                <li>聯繫管理員檢查服務器存儲</li>
              </ul>
            </div>
          ),
        });
        return;
      }
    }

    message.error(errorMessage);
  };

  // 格式化檔案大小
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };



  // 處理文件選擇前的驗證和添加
  const handleBeforeUpload = (file: File) => {
    console.log('📂 處理文件選擇:', file.name);

    // 檢查文件大小 (限制 50MB)
    const maxSize = 50 * 1024 * 1024;
    if (file.size > maxSize) {
      console.error(`文件 ${file.name} 超過 50MB 限制`);
      return false;
    }

    // 添加文件到列表
    setFileList(prev => [...prev, {
      uid: file.uid,
      name: file.name,
      status: 'done' as const, // 文件已選擇，等待上傳
      originFileObj: file,
    }]);

    console.log('📁 文件已添加到列表:', file.name);
    return false; // 阻止自動上傳
  };

  // 處理文件移除
  const handleRemove = (file: UploadFile) => {
    console.log('🗑️ 移除文件:', file.name);
    setFileList(prev => prev.filter(item => item.uid !== file.uid));
  };

  // Upload 組件配置
  const uploadProps: UploadProps = {
    name: 'file',
    multiple: true,
    fileList,
    beforeUpload: handleBeforeUpload,
    onRemove: handleRemove,
    disabled: uploading || !userInfo.tenant_id,
    showUploadList: {
      showPreviewIcon: false,
      showRemoveIcon: !uploading,
      showDownloadIcon: false,
    },
  };

  return (
    <div className="space-y-4">
      {/* 檔案上傳區域 */}
      <Dragger {...uploadProps} className="border-dashed border-2 transition-colors duration-300"
               style={{ borderColor: 'var(--color-border-secondary)' }}>
        <p className="ant-upload-drag-icon">
          <InboxOutlined style={{ color: 'var(--color-neon-blue)' }} />
        </p>
        <p className="ant-upload-text" style={{ color: 'var(--color-text-primary)' }}>
          點擊或拖拽檔案到此區域上傳
        </p>
        <p className="ant-upload-hint" style={{ color: 'var(--color-text-secondary)' }}>
          支援單個或批量上傳。嚴禁上傳公司數據或其他禁止的檔案
        </p>
      </Dragger>

      {/* 待上傳文件預覽 */}
      {fileList.length > 0 && (
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Text strong>待上傳文件 ({fileList.length})</Text>
            <Button
              type="primary"
              onClick={handleUpload}
              loading={uploading}
              disabled={fileList.length === 0}
              size="small"
            >
              {uploading ? '上傳中...' : `上傳 ${fileList.length} 個文件`}
            </Button>
          </div>
          <div className="max-h-32 overflow-y-auto space-y-1 p-2 bg-light-50 rounded">
            {fileList.map(file => (
              <div key={file.uid} className="flex items-center justify-between text-sm">
                <div className="flex items-center space-x-2">
                  <MdInsertDriveFile style={{ color: 'var(--color-neon-blue)' }} />
                  <span className="truncate">{file.name}</span>
                </div>
                <Text className="text-xs" style={{ color: 'var(--color-text-secondary)' }}>
                  {file.originFileObj ? `${(file.originFileObj.size / 1024).toFixed(2)} KB` : ''}
                </Text>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 上傳進度 */}
      {uploading && (
        <div className="p-4 bg-light-100 rounded-lg">
          <Progress percent={100} status="active" showInfo={false} />
          <Text className="text-sm" style={{ color: 'var(--color-text-secondary)' }}>正在上傳檔案...</Text>
        </div>
      )}

      {/* 檔案列表 */}
      {fileAttachments.length === 0 ? (
        <Empty 
          description="暫無檔案附件" 
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          style={{ color: 'var(--color-dark-500)' }}
        />
      ) : (
        <List
          loading={attachmentsLoading}
          dataSource={fileAttachments}
          renderItem={(item) => (
            <SpotlightCard
              className="mb-2"
              backgroundColor="var(--color-surface-primary)"
              spotlightColor="rgba(167, 139, 250, 0.2)"
              padding="16px"
              borderRadius={8}
            >
              <List.Item
                className="!p-0 !border-0"
                actions={[
                  <Button
                    key="download"
                    type="text"
                    icon={<MdDownload />}
                    onClick={() => handleDownload(item)}
                    style={{ color: 'var(--color-neon-blue)' }}
                  />,
                  <Popconfirm
                    key="delete"
                    title="確定要刪除這個檔案嗎？"
                    onConfirm={() => handleDelete(item.name)}
                    okText="確定"
                    cancelText="取消"
                  >
                    <Button
                      type="text"
                      icon={<MdDelete />}
                      danger
                    />
                  </Popconfirm>,
                ]}
              >
              <List.Item.Meta
                avatar={<MdInsertDriveFile style={{ color: 'var(--color-neon-blue)', fontSize: '20px' }} />}
                title={
                  <Text strong style={{ color: 'var(--color-text-primary)' }}>
                    {item.name}
                  </Text>
                }
                description={
                  <Space direction="vertical" size="small">
                    <Text className="text-sm" style={{ color: 'var(--color-text-secondary)' }}>
                      大小: {formatFileSize(item.file_size)}
                    </Text>
                    <Text className="text-sm" style={{ color: 'var(--color-text-secondary)' }}>
                      類型: {item.mime_type}
                    </Text>
                    {item.remark && (
                      <Text className="text-sm" style={{ color: 'var(--color-text-secondary)' }}>
                        備註: {item.remark}
                      </Text>
                    )}
                    <Text className="text-xs" style={{ color: 'var(--color-text-tertiary)' }}>
                      上傳時間: {new Date(item.created_at).toLocaleString()}
                    </Text>
                  </Space>
                }
              />
            </List.Item>
            </SpotlightCard>
          )}
        />
      )}


    </div>
  );
};

export default FileUploadTab;
